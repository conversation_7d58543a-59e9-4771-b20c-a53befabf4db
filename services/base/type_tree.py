from collections import UserString
from typing import Sequence

from services.base.domain.enums.data_types import DataType
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.infrastructure.database.opensearch.opensearch_index_constants import COLLECTIONS


class TreeNode(UserString):
    def __init__(self, path: str):
        if not path:
            raise ValueError("Path must be provided")
        super().__init__(path)
        split = path.split(".")
        self._path = path
        self._name = split[-1]
        self._parent_path = ".".join(split[:-1]) if len(split) > 1 else None
        self._data_type = self._get_data_type(split=split)
        self._collection = self._get_collection(split=split)

    @property
    def path(self) -> str:
        """The full dot-separated path of this node."""
        return self._path

    @property
    def name(self) -> str:
        """The name of this node (last component of the path)."""
        return self._name

    @property
    def parent_path(self) -> str | None:
        """The path of the parent node, if any."""
        return self._parent_path

    @property
    def data_type(self) -> DataType:
        """The DataType associated with this node."""
        return self._data_type

    @property
    def collection(self) -> str | None:
        """The collection associated with this node, if any."""
        return self._collection

    def __hash__(self) -> int:
        """Hash based on path for use in sets."""
        return hash(self._path)

    def __eq__(self, other) -> bool:
        """Equality based on path."""
        if isinstance(other, TreeNode):
            return self._path == other._path
        elif isinstance(other, str):
            return self._path == other
        return False

    def __lt__(self, other) -> bool:
        """Less than comparison based on path for sorting."""
        if isinstance(other, TreeNode):
            return self._path < other._path
        elif isinstance(other, str):
            return self._path < other
        return NotImplemented

    def __repr__(self) -> str:
        return f"TreeNode('{self._path}')"

    def _get_data_type(self, split: Sequence[str]) -> DataType:
        for s in split:
            if s in DataType:
                return DataType(s)
        raise ShouldNotReachHereException(f"DataType not found in path: {split}")

    def _get_collection(self, split: Sequence[str]) -> str | None:
        for s in split:
            if s in COLLECTIONS:
                return s
        return None

class TypeTree:
    __slots__ = ("_roots", "_all_nodes", "_node_cache")

    def __init__(self, paths: Sequence[str]):
        if not paths:
            raise ValueError("paths cannot be empty")

        self._roots: set[TreeNode] = set()
        self._all_nodes: set[TreeNode] = set()
        self._node_cache = {}

    def get_node(self, path: str) -> TreeNode:
        """Get a TreeNode object for the given path."""
        if path not in self._all_nodes:
            raise ValueError(f"Path '{path}' not found in tree")

        if path not in self._node_cache:
            self._node_cache[path] = TreeNode(path)

        return self._node_cache[path]

    def get_parent_node(self, node: TreeNode) -> TreeNode | None:
        """Get the parent TreeNode of the given node."""
        if node.parent_path is None:
            return None
        return self.get_node(node.parent_path)

    def get_children_nodes(self, node: TreeNode) -> Sequence[TreeNode]:
        """Get immediate children TreeNodes of the given node."""
        child_paths = self.children(node)
        return [self.get_node(path) for path in child_paths]

    def get_descendants_nodes(self, node: TreeNode, max_depth: int | None = None, leaves_only: bool = False) -> Sequence[TreeNode]:
        """Get all descendant TreeNodes of the given node."""
        descendant_paths = self.dfs(node, collect_paths=False, max_depth=max_depth)
        descendant_paths = [p for p in descendant_paths if p != node]

        nodes = [self.get_node(path) for path in descendant_paths]

        if leaves_only:
            nodes = [node for node in nodes if self.is_leaf(node)]

        return nodes

    def get_root_nodes(self) -> Sequence[TreeNode]:
        """Get all root nodes as TreeNode objects."""
        return [self.get_node(path) for path in sorted(self._roots)]


if __name__ == "__main__":
    # Example usage of the new TreeNode-based TypeTree
    t = TypeTree([
        "activity.app.productivity",
        "activity.app.interactive.augmented_reality",
        "content.movie",
        "content.series",
        "content.sport",
        "exercise.cardio.running",
        "exercise.cardio.cycling",
        "exercise.strength.weights",
        "exercise.strength.yoga",
        "nutrition.food.meat",
        "nutrition.drink.milk",
    ])

    # Get a TreeNode object
    exercise_node = t.get_node("exercise")
    print(f"Node: {exercise_node}")
    print(f"Path: {exercise_node.path}")
    print(f"Name: {exercise_node.name}")
    print(f"Data Type: {exercise_node.data_type}")
    print(f"Collection: {exercise_node.collection}")
    print(f"Parent Path: {exercise_node.parent_path}")

    # Get parent through tree
    parent_node = t.get_parent_node(exercise_node)
    print(f"Parent: {parent_node.path if parent_node else None}")

    # Get children through tree
    children = t.get_children_nodes(exercise_node)
    print(f"Children: {[child.path for child in children]}")

    # Get all descendants through tree
    descendants = t.get_descendants_nodes(exercise_node)
    print(f"All descendants: {[desc.path for desc in descendants]}")

    # Get only leaf descendants through tree
    leaf_descendants = t.get_descendants_nodes(exercise_node, leaves_only=True)
    print(f"Leaf descendants: {[desc.path for desc in leaf_descendants]}")

    # TreeNode can be used as a string
    print(f"TreeNode as string: '{exercise_node}'")

    # TreeNode can be used in sets (hashable and comparable)
    node_set = {exercise_node, t.get_node("content"), t.get_node("nutrition")}
    print(f"Nodes in set: {sorted(node.path for node in node_set)}")

    # Iterate over all nodes in the tree
    print("All nodes in tree:")
    for node in t:
        print(f"  {node.path} -> {node.data_type}")

    # Get root nodes
    root_nodes = t.get_root_nodes()
    print(f"Root nodes: {[root.path for root in root_nodes]}")
